using IntegrationTests.Infrastructure;
using Raven.Client.Documents;
using Raven.Client.Documents.Session;
using Shouldly;
using System.Net;
using System.Net.Http;
using ToroEhr.Enums;
using ToroEhr.Features.Appointment;

namespace IntegrationTests.Appointment;

[Trait("Cancel Appointment", "Scenarios")]
public class CancelAppointmentBeforeCheckInWindow : IClassFixture<CancelAppointmentBeforeCheckInWindowFixture>
{
    private readonly CancelAppointmentBeforeCheckInWindowFixture _fixture;

    public CancelAppointmentBeforeCheckInWindow(CancelAppointmentBeforeCheckInWindowFixture fixture) =>
        _fixture = fixture;

    [Fact(DisplayName = "1. Appointment canceled before check-in window has no fee")]
    public async Task AppointmentCanceledWithoutFee()
    {
        using IAsyncDocumentSession session = _fixture.DocumentStore.OpenAsyncSession();

        List<ToroEhr.Domain.Appointment> appointments =
            await session.Query<ToroEhr.Domain.Appointment>()
            .ToListAsync();

        appointments.ShouldHaveSingleItem();
        appointments.First().Status.ShouldBe(AppointmentStatus.Canceled.Name);
    }

    [Fact(DisplayName = "2. Encounter is canceled and should NOT appear in billing")]
    public async Task EncounterNotInBillingSystem()
    {
        using IAsyncDocumentSession session = _fixture.DocumentStore.OpenAsyncSession();

        List<ToroEhr.Domain.Encounter> encounters =
            await session.Query<ToroEhr.Domain.Encounter>()
            .ToListAsync();

        encounters.ShouldHaveSingleItem();
        var encounter = encounters.First();
        encounter.Status.ShouldBe(EncounterStatus.Canceled.Name);
        encounter.ShouldAppearInPayments().ShouldBeFalse();
    }

    [Fact(DisplayName = "3. Paid encounters should appear in billing for refunds/additional charges")]
    public async Task PaidEncounterAppearsInBilling()
    {
        using IAsyncDocumentSession session = _fixture.DocumentStore.OpenAsyncSession();

        // Create a paid encounter
        var paidEncounter = ToroEhr.Domain.Encounter.Create(_fixture.Patient.Id, _fixture.EmployeeDoctor.Id, _fixture.Location.Id, _fixture.Location.Classification, DateTimeOffset.Now.AddDays(-1));
        paidEncounter.SetToPaid();
        await session.StoreAsync(paidEncounter);
        await session.SaveChangesAsync();

        paidEncounter.ShouldAppearInPayments().ShouldBeTrue();
    }
}

[Trait("Cancel Appointment", "Scenarios")]
public class CancelAppointmentWithinCheckInWindow : IClassFixture<CancelAppointmentWithinCheckInWindowFixture>
{
    private readonly CancelAppointmentWithinCheckInWindowFixture _fixture;

    public CancelAppointmentWithinCheckInWindow(CancelAppointmentWithinCheckInWindowFixture fixture) =>
        _fixture = fixture;

    [Fact(DisplayName = "1. Appointment canceled within check-in window is marked as missed")]
    public async Task AppointmentMarkedAsMissed()
    {
        using IAsyncDocumentSession session = _fixture.DocumentStore.OpenAsyncSession();

        List<ToroEhr.Domain.Appointment> appointments =
            await session.Query<ToroEhr.Domain.Appointment>()
            .ToListAsync();

        appointments.ShouldHaveSingleItem();
        appointments.First().Status.ShouldBe(AppointmentStatus.Missed.Name);
    }

    [Fact(DisplayName = "2. Encounter is marked as missed and SHOULD appear in billing")]
    public async Task EncounterMarkedAsMissedInBillingSystem()
    {
        using IAsyncDocumentSession session = _fixture.DocumentStore.OpenAsyncSession();

        List<ToroEhr.Domain.Encounter> encounters =
            await session.Query<ToroEhr.Domain.Encounter>()
            .ToListAsync();

        encounters.ShouldHaveSingleItem();
        var encounter = encounters.First();
        encounter.Status.ShouldBe(EncounterStatus.Missed.Name);
        encounter.ShouldAppearInPayments().ShouldBeTrue();
    }
}

public class CancelAppointmentBeforeCheckInWindowFixture : BaseFixture
{
    public HttpResponseMessage? Response;

    public override async Task InitializeAsync()
    {
        await base.InitializeAsync();
        await CreateEmployeeDoctorUserWithSession(Session);
        HttpClient client = CreatePatientClient();

        // Create encounter first
        var encounter = ToroEhr.Domain.Encounter.Create(Patient.Id, EmployeeDoctor.Id, Location.Id, Location.Classification, DateTimeOffset.Now.AddDays(5));
        await Session.StoreAsync(encounter);

        // Create appointment 5 days in future (outside 48-hour check-in window)
        var appointment = ToroEhr.Domain.Appointment.Create(Patient.Id, EmployeeDoctor.Id, Location.Id, encounter.Id,
            startAt: DateTimeOffset.Now.AddDays(5), endAt: DateTimeOffset.Now.AddDays(5).AddMinutes(30), 30, AppointmentStatus.Confirmed);
        await Session.StoreAsync(appointment);
        await Session.SaveChangesAsync();

        var command = new CancelAppointmentCommand(appointment.Id);
        StringContent requestContent = BuildRequestContent(command);

        var request = new HttpRequestMessage(HttpMethod.Delete, "appointments")
        {
            Content = requestContent
        };

        Response = await client.SendAsync(request);
    }
}

public class CancelAppointmentWithinCheckInWindowFixture : BaseFixture
{
    public HttpResponseMessage? Response;

    public override async Task InitializeAsync()
    {
        await base.InitializeAsync();
        await CreateEmployeeDoctorUserWithSession(Session);
        HttpClient client = CreatePatientClient();

        // Create encounter first
        var encounter = ToroEhr.Domain.Encounter.Create(Patient.Id, EmployeeDoctor.Id, Location.Id, Location.Classification, DateTimeOffset.Now.AddMinutes(30));
        await Session.StoreAsync(encounter);
        
        // Create appointment 30 minutes in future (within check-in window, assuming default 24 hours)
        var appointment = ToroEhr.Domain.Appointment.Create(Patient.Id, EmployeeDoctor.Id, Location.Id, encounter.Id,
            startAt: DateTimeOffset.Now.AddMinutes(30), endAt: DateTimeOffset.Now.AddMinutes(60), 30, AppointmentStatus.Confirmed);
        await Session.StoreAsync(appointment);
        await Session.SaveChangesAsync();

        var command = new CancelAppointmentCommand(appointment.Id);
        StringContent requestContent = BuildRequestContent(command);

        var request = new HttpRequestMessage(HttpMethod.Delete, "appointments")
        {
            Content = requestContent
        };

        Response = await client.SendAsync(request);
    }
}
