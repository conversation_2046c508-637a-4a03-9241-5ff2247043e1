using System.Net.Http.Headers;
using System.Reflection;
using System.Text;
using System.Text.Json;
using DiffEngine;
using FakeItEasy;
using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.Extensions.DependencyInjection;
using Raven.Client.Documents;
using Raven.Client.Documents.Indexes;
using Raven.Client.Documents.Session;
using Raven.Embedded;
using Raven.TestDriver;
using ToroEhr.Domain;
using ToroEhr.Enums;
using ToroEhr.Services;
using ToroEhr.Shared;
using TestServerOptions = Raven.TestDriver.TestServerOptions;

namespace IntegrationTests.Infrastructure;

public class BaseFixture : RavenTestDriver, IAsyncLifetime
{
    public readonly IDocumentStore DocumentStore;
    public readonly IAsyncDocumentSession Session;
    private readonly WebApplicationFactory<Program>? _factory;
    private static readonly string SuperUserAccessToken = Utils.GenerateAccessToken();
    private static readonly string PatientAccessToken = Utils.GenerateAccessToken();
    private static readonly string EmployeeAdminAccessToken = Utils.GenerateAccessToken();
    private static readonly string EmployeeDoctorAccessToken = Utils.GenerateAccessToken();
    public ToroEhr.Domain.Organization Organization;
    public Location Location;
    public ToroEhr.Domain.Patient Patient;
    public ToroEhr.Domain.Employee EmployeeAdmin;
    public ToroEhr.Domain.Employee EmployeeDoctor;

    protected BaseFixture()
    {
        var testServerOptions = new TestServerOptions
        {
            Licensing = new ServerOptions.LicensingOptions() { }
        };

        ConfigureServer(testServerOptions);

        DocumentStore = GetDocumentStore();
        IndexCreation.CreateIndexes(Assembly.Load("ToroEhr"), DocumentStore);

        DotNetEnv.Env.Load(Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "..", "..", "..", ".env"));

        var fakeEmailService = A.Fake<EmailService>();
        A.CallTo(() => fakeEmailService.SendEmailAsync(A<string>._, A<string>._, A<string>._, null, null))
            .Returns(Task.CompletedTask);

        _factory = new WebApplicationFactory<Program>()
            .WithWebHostBuilder(builder =>
            {
                builder.ConfigureServices(services =>
                {
                    // Replace IDocumentStore with test instance
                    services.AddSingleton<IDocumentStore>(DocumentStore);
                    services.AddSingleton(fakeEmailService);
                });
            });
        Session = DocumentStore.OpenAsyncSession();
        DiffRunner.Disabled = true;
    }

    public virtual async Task InitializeAsync()
    {
        await SeedData();
        await Session.SaveChangesAsync();
        Session.Advanced.WaitForIndexesAfterSaveChanges();
    }

    public async Task DisposeAsync()
    {
        if (_factory is not null)
        {
            await _factory.DisposeAsync().AsTask();
        }
        
        DocumentStore.Dispose();
        await Task.CompletedTask;
    }

    private HttpClient CreateAuthenticatedClient(string token)
    {
        var client = _factory!.CreateClient();
        client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);
        return client;
    }

    protected HttpClient CreateSuperAdminClient() => CreateAuthenticatedClient(SuperUserAccessToken);
    
    protected HttpClient CreateEmployeeAdminClient() => CreateAuthenticatedClient(EmployeeAdminAccessToken);

    protected HttpClient CreateEmployeeDoctorClient() => CreateAuthenticatedClient(EmployeeDoctorAccessToken);

    protected HttpClient CreatePatientClient() => CreateAuthenticatedClient(PatientAccessToken);

    private async Task SeedData()
    {       
        await CreateOrganization(Session);
        await CreateLocation(Session);
        await CreateSuperUserWithSession(Session);
        await CreatePatientUserWithSession(Session);
        await CreateEmployeeAdminUserWithSession(Session);
    }
    
    private async Task CreateOrganization(IAsyncDocumentSession session)
    {
        Organization = ToroEhr.Domain.Organization.Create("Vitalis");
        await session.StoreAsync(Organization);
    }

    private async Task CreateLocation(IAsyncDocumentSession session)
    {
        Location = Location.Create(Organization.Id, "Location 1", "Ambulatory", true, "123456789", "123456789", 48, 0, 48,
            Address.Create("Some street", "Some city", "Some country", "12345"), null);
        await session.StoreAsync(Location);
    }

    private static async Task CreateSuperUserWithSession(IAsyncDocumentSession session)
    {
        User user = User.Create("<EMAIL>", "Test", "User", null, null, new List<string> { UserRole.SuperAdmin });
        UserSession userSession = UserSession.Create(user.Id, null, null, UserRole.SuperAdmin, SuperUserAccessToken, "**********",
            DateTime.UtcNow, DateTime.UtcNow, DateTime.UtcNow);

        await session.StoreAsync(user);
        await session.StoreAsync(userSession);
    }

    private async Task CreatePatientUserWithSession(IAsyncDocumentSession session)
    {
        Patient = ToroEhr.Domain.Patient.Create("TEST123456", "<EMAIL>", "PatientName", "PatientLastName", DateTime.UtcNow,
            "**********");
        User user = User.Create("<EMAIL>", "Test", "User", Patient.Id, null, new List<string> { UserRole.Patient.Name });
        OrganizationPatient organizationPatient =
            OrganizationPatient.Create(Patient.Id, Organization.Id, "", DateTime.UtcNow);
        UserSession userSession = UserSession.Create(user.Id, null, null, UserRole.Patient, PatientAccessToken, "**********",
            DateTime.UtcNow, DateTime.UtcNow, DateTime.UtcNow);

        await session.StoreAsync(Patient);
        await session.StoreAsync(organizationPatient);
        await session.StoreAsync(user);
        await session.StoreAsync(userSession);
    }

    private async Task CreateEmployeeAdminUserWithSession(IAsyncDocumentSession session)
    {
        EmployeeAdmin = ToroEhr.Domain.Employee.Create("<EMAIL>", "EmployeeName", "EmployeeLastName",
            "123456789", "111222333", Address.Create("Some street", "Some city", "Some country", "12345"));
        User user = User.Create("<EMAIL>", "Test", "User", null, EmployeeAdmin.Id, new List<string> { UserRole.Employee.Name });
        LocationEmployee organizationEmployee = LocationEmployee.Create(Organization.Id, Location.Id,
            EmployeeAdmin.Id, [EmployeeRole.OrganizationAdmin.Name], "", "", "", DateTime.UtcNow, false);

        await session.StoreAsync(EmployeeAdmin);
        await session.StoreAsync(user);
        await session.StoreAsync(organizationEmployee);
    }

    protected async Task CreateEmployeeDoctorUserWithSession(IAsyncDocumentSession session)
    {
        EmployeeDoctor = ToroEhr.Domain.Employee.Create("<EMAIL>", "EmployeeDoctorName", "EmployeeDoctorLastName",
            "123456789", "111222333", Address.Create("Some street", "Some city", "Some country", "12345"));
        User user = User.Create("<EMAIL>", "Test", "User", null, EmployeeDoctor.Id, [UserRole.Employee.Name]);
        LocationEmployee organizationEmployee = LocationEmployee.Create(Organization.Id, Location.Id,
            EmployeeDoctor.Id, [EmployeeRole.Practitioner.Name], "", "", "", DateTime.UtcNow, false);

        organizationEmployee.UpdateGeneralSettings("test", 2, 30, false, new List<string>());
        List<ToroEhr.ValueObjects.OfficeHours> officeHours = new List<ToroEhr.ValueObjects.OfficeHours>
        {
            new ToroEhr.ValueObjects.OfficeHours(
                Day: DayOfWeek.Monday,
                OpenTime: new TimeSpan(9,0,0),
                CloseTime: new TimeSpan(17,0,0),
                Exclusions: new List<ToroEhr.ValueObjects.Exclusion>
                {
                    new ToroEhr.ValueObjects.Exclusion
                    ( From: new TimeSpan(12,0,0),
                        To: new TimeSpan(12,30,0),
                        Title: "Lunch break"
                    )
                }
            )
        };
        organizationEmployee.UpdateOfficeHours(officeHours);

        UserSession userSession = UserSession.Create(user.Id, Organization.Id, Location.Id, UserRole.Employee, EmployeeDoctorAccessToken, "**********",
            DateTime.UtcNow, DateTime.UtcNow, DateTime.UtcNow);

        await session.StoreAsync(EmployeeDoctor);
        await session.StoreAsync(user);
        await session.StoreAsync(organizationEmployee);
        await session.StoreAsync(userSession);
    }

    public static async Task<T?> GetRequestContent<T>(
        HttpResponseMessage httpResponseMessage)
    {
        JsonSerializerOptions jsonSettings = new()
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            WriteIndented = true,
        };

        return JsonSerializer.Deserialize<T>(
            await httpResponseMessage.Content.ReadAsStringAsync(),
            jsonSettings);
    }

    protected static StringContent BuildRequestContent<T>(T content)
    {
        string serialized = JsonSerializer.Serialize(content);

        return new StringContent(serialized, Encoding.UTF8, "application/json");
    }
}