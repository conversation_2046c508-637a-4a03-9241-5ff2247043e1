using MediatR;
using Microsoft.AspNetCore.Mvc;
using ToroEhr.Features.Encounter;
using ToroEhr.Features.Patient;
using ToroEhr.Features.Patient.PatientInsurance;
using ToroEhr.Features.Patient.Shared;
using ToroEhr.Features.PatientImmunization;
using ToroEhr.Shared;
using VitalSignResponse = ToroEhr.Features.Patient.VitalSignResponse;

namespace ToroEhr.Controllers;

[ApiController]
[Route("patients")]
[Produces("application/json")]
public class PatientController : ControllerBase
{
    private readonly IMediator _mediator;

    public PatientController(IMediator mediator)
    {
        _mediator = mediator;
    }

    /// <summary>
    ///     List patients
    /// </summary>
    /// <returns></returns>
    [HttpGet]
    public async Task<ActionResult<PaginatedList<PatientResponse>>> ListPatients([FromQuery] int? pageNumber,
        [FromQuery] int? pageSize, [FromQuery] string? searchParam)
    {
        var result =
            await _mediator.Send(new ListPatientsQuery(new PagedSearchParams(pageNumber, pageSize, searchParam)));
        return Ok(result);
    }

    /// <summary>
    ///     Get patient by MRN
    /// </summary>
    /// <returns></returns>
    [HttpGet("by-mrn/{mrn}")]
    public async Task<ActionResult<PatientResponse?>> GetPatientByMrn(string mrn)
    {
        var result = await _mediator.Send(new GetPatientByMrnQuery(mrn));
        return Ok(result);
    }

    /// <summary>
    ///     Get patient profile
    /// </summary>
    /// <returns></returns>
    [HttpGet("profile")]
    public async Task<ActionResult<PatientProfileResponse>> GetPatientProfile(string? patientId)
    {
        var result = await _mediator.Send(new GetPatientProfileQuery(patientId));
        return Ok(result);
    }

    /// <summary>
    ///     Set patient personal information
    /// </summary>
    /// <returns></returns>
    [HttpPost("personal-info")]
    public async Task<ActionResult<string>> SetPersonalInformation([FromForm] SetPatientPersonalInfoCommand command)
    {
        var result = await _mediator.Send(command);
        return Ok(result);
    }

    /// <summary>
    ///     Set patient contact information
    /// </summary>
    /// <returns></returns>
    [HttpPost("contact-info")]
    public async Task<ActionResult<string>> SetContactInformation([FromBody] SetPatientContactInfoCommand command)
    {
        var result = await _mediator.Send(command);
        return Ok(result);
    }

    /// <summary>
    ///     Set patient personal information
    /// </summary>
    /// <returns></returns>
    [HttpPut("medications")]
    public async Task<ActionResult<string>> SetMedications([FromBody] IEnumerable<PatientMedicationRequest> request)
    {
        var result = await _mediator.Send(new SetPatientMedicationsCommand(request));
        return Ok(result);
    }

    /// <summary>
    ///     Set patient personal information
    /// </summary>
    /// <returns></returns>
    [HttpPut("allergies")]
    public async Task<ActionResult<string>> SetAllergies([FromBody] IEnumerable<PatientAllergyRequest> request)
    {
        var result = await _mediator.Send(new SetPatientAllergiesCommand(request));
        return Ok(result);
    }

    /// <summary>
    ///     List patient immunizations
    /// </summary>
    /// <returns></returns>
    [HttpGet("immunizations")]
    public async Task<ActionResult<List<PatientImmunizationResponse>>> ListImmunizations(string? patientId)
    {
        var result = await _mediator.Send(new GetPatientImmunizationQuery(patientId));
        return Ok(result);
    }

    /// <summary>
    ///     Add patient immunizations
    /// </summary>
    /// <returns></returns>
    [HttpPost("immunizations")]
    public async Task<ActionResult> AddImmunization([FromBody] AddPatientImmunizationCommand command)
    {
        var result = await _mediator.Send(command);
        return Ok(result);
    }

    /// <summary>
    ///     Delete patient immunizations
    /// </summary>
    /// <returns></returns>
    [HttpDelete("immunizations")]
    public async Task<ActionResult> DeleteImmunization([FromBody] DeletePatientImmunizationCommand command)
    {
        var result = await _mediator.Send(command);
        return Ok(result);
    }

    /// <summary>
    ///     List patient insurances
    /// </summary>
    /// <returns></returns>
    [HttpGet("insurances")]
    public async Task<ActionResult<PatientInsuranceResponse>> ListInsurances()
    {
        var result = await _mediator.Send(new ListPatientInsuranceQuery());
        return Ok(result);
    }

    /// <summary>
    ///     Get patient insurance info (copay from primary insurance and insurance status)
    /// </summary>
    /// <returns></returns>
    [HttpGet("insurance-info")]
    public async Task<ActionResult<PatientInsuranceInfoResponse>> GetInsuranceInfo(string? patientId)
    {
        var result = await _mediator.Send(new GetPatientInsuranceInfoQuery(patientId));
        return Ok(result);
    }

    /// <summary>
    ///     Add patient insurance
    /// </summary>
    /// <returns></returns>
    [HttpPost("insurances")]
    public async Task<ActionResult> AddInsurance([FromForm] AddPatientInsuranceCommand command)
    {
        var result = await _mediator.Send(command);
        return Ok(result);
    }

    /// <summary>
    ///     Delete patient insurance
    /// </summary>
    /// <returns></returns>
    [HttpDelete("insurances")]
    public async Task<ActionResult> DeleteInsurance([FromBody] DeletePatientInsuranceCommand command)
    {
        var result = await _mediator.Send(command);
        return Ok(result);
    }

    /// <summary>
    ///     List patient records
    /// </summary>
    /// <returns></returns>
    [HttpGet("records")]
    public async Task<ActionResult<PaginatedList<PatientRecordResponse>>> ListRecords([FromQuery] int? pageNumber,
        [FromQuery] int? pageSize, [FromQuery] string? searchParam)
    {
        var result =
            await _mediator.Send(new ListPatientRecordsQuery(new PagedSearchParams(pageNumber, pageSize, searchParam)));
        return Ok(result);
    }

    /// <summary>
    ///     Get laboratory results
    /// </summary>
    /// <returns></returns>
    [HttpGet("{id}/laboratory")]
    public async Task<ActionResult<List<LaboratoryResultResponse>>> GetLaboratory(string id)
    {
        var result = await _mediator.Send(new GetLaboratoryResultsForPatientQuery(id));
        return Ok(result);
    }

    /// <summary>
    ///     Mark laboratory results as seen
    /// </summary>
    /// <returns></returns>
    [HttpPut("laboratory")]
    public async Task<ActionResult<List<LaboratoryResultResponse>>> MarkLaboratoryResultAsSeen(
        [FromBody] MarkLaboratoryResultAsSeenCommand command)
    {
        var result = await _mediator.Send(command);
        return Ok(result);
    }

    /// <summary>
    ///     Get imaging results
    /// </summary>
    /// <returns></returns>
    [HttpGet("{id}/imaging")]
    public async Task<ActionResult<List<ImagingResultResponse>>> GetImagingResults(string id)
    {
        var result = await _mediator.Send(new GetImagingResultsForPatientQuery(id));
        return Ok(result);
    }

    /// <summary>
    ///     Mark imaging result as seen
    /// </summary>
    /// <returns></returns>
    [HttpPut("imaging")]
    public async Task<ActionResult<List<LaboratoryResultResponse>>> MarkImagingResultAsSeen(
        [FromBody] MarkImagingResultAsSeenCommand command)
    {
        var result = await _mediator.Send(command);
        return Ok(result);
    }

    /// <summary>
    ///     Get vital sings
    /// </summary>
    /// <returns></returns>
    [HttpGet("{id}/vital-signs")]
    public async Task<ActionResult<List<VitalSignResponse>>> ListVitalSigns(string id)
    {
        var result = await _mediator.Send(new ListVitalSignsQuery(id));
        return Ok(result);
    }
    
    /// <summary>
    ///     Get payment cards
    /// </summary>
    /// <returns></returns>
    [HttpGet("{id}/payment-cards")]
    public async Task<ActionResult<List<PaymentCardResponse>>> ListPaymentCards(string id)
    {
        var result = await _mediator.Send(new ListPatientPaymentCardsQuery(id));
        return Ok(result);
    }

    /// <summary>
    ///     List patient messages
    /// </summary>
    /// <returns></returns>
    [HttpGet("messages")]
    public async Task<ActionResult<PaginatedList<PatientCommunicationMessageResponse>>> ListMessages([FromQuery] int? pageNumber,
        [FromQuery] int? pageSize, [FromQuery] string? searchParam)
    {
        var result =
            await _mediator.Send(new ListPatientCommunicationMessagesQuery(new PagedSearchParams(pageNumber, pageSize, searchParam)));
        return Ok(result);
    }

    /// <summary>
    ///     Get active medications
    /// </summary>
    /// <returns></returns>
    [HttpGet("{id}/active-medications")]
    public async Task<ActionResult<List<PatientActiveMedication>>> ListPatientActiveMedicationOrders(string id)
    {
        var result = await _mediator.Send(new ListPatientActiveMedicationOrdersQuery(id));
        return Ok(result);
    }
}