using MediatR;
using Microsoft.AspNetCore.Mvc;
using ToroEhr.Features.Notification;
using ToroEhr.Shared;

namespace ToroEhr.Controllers;

[ApiController]
[Route("api/[controller]")]
public class NotificationController : ControllerBase
{
    private readonly IMediator _mediator;

    public NotificationController(IMediator mediator)
    {
        _mediator = mediator;
    }

    [HttpGet]
    public async Task<ActionResult<PaginatedList<NotificationResponse>>> GetUserNotifications(
        [FromQuery] PagedSearchParams pagedSearchParams)
    {
        var query = new GetUserNotificationsQuery(pagedSearchParams);
        var result = await _mediator.Send(query);
        return Ok(result);
    }

    [HttpPut("{id}/read")]
    public async Task<ActionResult> MarkNotificationAsRead(string id)
    {
        var command = new MarkNotificationAsReadCommand(id, DateTime.UtcNow);
        await _mediator.Send(command);
        return NoContent();
    }

    [HttpDelete("{id}")]
    public async Task<ActionResult> DeleteNotification(string id)
    {
        var command = new DeleteNotificationCommand(id);
        await _mediator.Send(command);
        return NoContent();
    }

    [HttpGet("unread-count")]
    public async Task<ActionResult<int>> GetUnreadNotificationCount()
    {
        var query = new GetUnreadNotificationCountQuery();
        var result = await _mediator.Send(query);
        return Ok(result);
    }
}
