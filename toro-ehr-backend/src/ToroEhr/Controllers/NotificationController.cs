using MediatR;
using Microsoft.AspNetCore.Mvc;
using ToroEhr.Features.Notification;
using ToroEhr.Shared;

namespace ToroEhr.Controllers;

[ApiController]
[Route("notifications")]
public class NotificationController : ControllerBase
{
    private readonly IMediator _mediator;

    public NotificationController(IMediator mediator)
    {
        _mediator = mediator;
    }

    [HttpGet]
    public async Task<ActionResult<PaginatedList<NotificationResponse>>> GetUserNotifications(
        [FromQuery] int? pageNumber, [FromQuery] int? pageSize)
    {
        var query = new GetUserNotificationsQuery(new PagedSearchParams(pageNumber, pageSize));
        var result = await _mediator.Send(query);
        return Ok(result);
    }

    [HttpPut("{id}/read")]
    public async Task<ActionResult> MarkNotificationAsRead(string id)
    {
        var command = new MarkNotificationAsReadCommand(id);
        await _mediator.Send(command);
        return NoContent();
    }

    [HttpDelete("{id}")]
    public async Task<ActionResult> DeleteNotification(string id)
    {
        var command = new DeleteNotificationCommand(id);
        await _mediator.Send(command);
        return NoContent();
    }

    [HttpGet("unread-count")]
    public async Task<ActionResult<int>> GetUnreadNotificationCount()
    {
        var query = new GetUnreadNotificationCountQuery();
        var result = await _mediator.Send(query);
        return Ok(result);
    }
}
