using Ardalis.SmartEnum;

namespace ToroEhr.Enums;

public sealed class NotificationType : SmartEnum<NotificationType, string>
{
    public static readonly NotificationType AppointmentConfirmed = new NotificationType(nameof(AppointmentConfirmed), "Appointment Confirmed");
    public static readonly NotificationType AppointmentRequested = new NotificationType(nameof(AppointmentRequested), "Appointment Requested");
    public static readonly NotificationType AppointmentRescheduled = new NotificationType(nameof(AppointmentRescheduled), "Appointment Rescheduled");
    public static readonly NotificationType AppointmentCanceled = new NotificationType(nameof(AppointmentCanceled), "Appointment Canceled");
    public static readonly NotificationType AppointmentMissed = new NotificationType(nameof(AppointmentMissed), "Appointment Missed");

    private NotificationType(string name, string value) : base(name, value)
    {
    }
}
