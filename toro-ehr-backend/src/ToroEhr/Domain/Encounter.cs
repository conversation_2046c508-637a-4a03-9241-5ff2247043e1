using Newtonsoft.Json;
using ToroEhr.Enums;

namespace ToroEhr.Domain;

public sealed class Encounter : Entity
{
    [JsonConstructor]
    public Encounter(string patientId, string practitionerId, string locationId, string classification, DateTimeOffset startAt)
    {
        PatientId = patientId;
        PractitionerId = practitionerId;
        LocationId = locationId;
        Classification = classification;
        Status = EncounterStatus.Planned.Name;
        StartAt = startAt;
    }

    public string LocationId { get; private set; }
    public string Classification { get; private set; }
    
    public DateTimeOffset StartAt { get; private set; }
    public string PatientId { get; private set; }
    public string PractitionerId { get; private set; }

    public string Status { get; private set; }

    public string PaymentStatus { get; private set; } = "Unpaid";
    public string? ScratchText { get; private set; }
    public DateTimeOffset? StartedAt { get; private set; }
    public DateTimeOffset? CompletedAt { get; private set; }

    public static Encounter Create(string patientId, string practitionerId, string locationId, string classification,
        DateTimeOffset startAt) => new(patientId, practitionerId, locationId, classification, startAt);

    public void UpdateScratchText(string scratchText)
    {
        ScratchText = scratchText;
    }

    public void UpdateStatus(EncounterStatus status, DateTimeOffset? startedAt)
    {
        if (status == EncounterStatus.InProgress)
        {
            StartedAt = startedAt;
        }
        
        Status = status.Name;
    }
    
    public void SetToPaid()
    {
        PaymentStatus = "Paid";
    }

    public void Checkout()
    {
        Status = EncounterStatus.Completed.Name;
        CompletedAt = DateTimeOffset.UtcNow;
    }

    public void Checkout(DateTimeOffset completedAt)
    {
        Status = EncounterStatus.Completed.Name;
        CompletedAt = completedAt;
    }

    public void Cancel()
    {
        Status = EncounterStatus.Canceled.Name;
    }

    public void MarkAsMissed()
    {
        Status = EncounterStatus.Missed.Name;
    }

    public bool ShouldAppearInPayments()
    {
        // Show in payments if:
        // - Not canceled (free cancellation) OR
        // - Missed (should be charged) OR
        // - Any other status (allows refunds/additional charges on paid encounters)
        return Status != EncounterStatus.Canceled.Name;
    }
}