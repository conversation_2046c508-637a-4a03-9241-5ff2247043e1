using MediatR;
using Raven.Client.Documents.Session;
using Raven.Client.Documents;
using ToroEhr.Infrastructure.Guards;
using ToroEhr.Infrastructure.Mediatr.Auth;
using ToroEhr.Services;

namespace ToroEhr.Features.Appointment;

public sealed record CancelAppointmentCommand(
    string Id) : AuthRequest<string>
{
}

internal class CancelAppointmentAuth : IAuth<CancelAppointmentCommand, string>
{
    public CancelAppointmentAuth(Authenticator authenticator)
    {
        var user = authenticator.User;
    }
}

internal class CancelAppointmentHandler : IRequestHandler<CancelAppointmentCommand, string>
{
    private readonly IDocumentStore _store;
    private readonly EmailService _emailService;
    private readonly Authenticator _authenticator;

    public CancelAppointmentHandler(IDocumentStore store, EmailService emailService, Authenticator authenticator)
    {
        _store = store;
        _emailService = emailService;
        _authenticator = authenticator;
    }

    public async Task<string> Handle(CancelAppointmentCommand command,
        CancellationToken cancellationToken)
    {
        using IAsyncDocumentSession session = _store.OpenAsyncSession();

        Domain.Appointment appointment = await session
            .Include<Domain.Appointment>(x => x.LocationId)
            .LoadAsync<Domain.Appointment>(command.Id, cancellationToken);

        Guard.AgainstNotFound(appointment, new("Appointment.NotFound", $"Appointment with 'id:' {command.Id} not found!"));

        Domain.Location location = await session.LoadAsync<Domain.Location>(appointment.LocationId, cancellationToken);
        Guard.AgainstNotFound(location, new("Location.NotFound", $"Location with 'id:' {appointment.LocationId} not found!"));

        bool isWithinCheckInWindow = appointment.IsWithinCheckInWindow(command.Timestamp, location.CheckInStartOffsetHours);

        // If appointment has an encounter (confirmed appointment)
        if (!string.IsNullOrEmpty(appointment.EncounterId))
        {
            Domain.Encounter encounter = await session.LoadAsync<Domain.Encounter>(appointment.EncounterId, cancellationToken);
            if (encounter != null)
            {
                if (isWithinCheckInWindow)
                {
                    encounter.MarkAsMissed();
                }
                else
                {
                    encounter.Cancel();
                }
                await session.StoreAsync(encounter, cancellationToken);
            }
        }

        // Mark appointment as missed if within check-in window, otherwise canceled
        if (isWithinCheckInWindow)
        {
            appointment.MarkAsMissed();
        }
        else
        {
            appointment.Cancel();
        }
        await session.StoreAsync(appointment, cancellationToken);

        // create notification for the opposite party
        var user = _authenticator.User;
        var timestamp = DateTime.UtcNow;

        if (user.IsPatient)
        {
            // patient canceled - notify employee
            Domain.LocationEmployee locationEmployee = await session.Query<Domain.LocationEmployee>()
                .Where(x => x.EmployeeId == appointment.EmployeeId && x.LocationId == appointment.LocationId)
                .FirstOrDefaultAsync(cancellationToken);

            if (locationEmployee != null)
            {
                await NotificationService.CreateAppointmentCanceledByPatientNotification(
                    session,
                    appointment.EmployeeId,
                    appointment.Id,
                    locationEmployee.ReceivedNotificationPreferences,
                    timestamp);
            }
        }
        else if (user.SelectedUserRole == ToroEhr.Enums.UserRole.Employee.Name)
        {
            // employee canceled - notify patient
            await NotificationService.CreateAppointmentCanceledByEmployeeNotification(
                session,
                appointment.PatientId,
                appointment.Id,
                timestamp);
        }

        await session.SaveChangesAsync(cancellationToken);

        return appointment.Id;
    }
}
