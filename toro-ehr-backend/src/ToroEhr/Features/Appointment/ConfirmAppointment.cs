using MediatR;
using Raven.Client.Documents.Session;
using Raven.Client.Documents;
using ToroEhr.Features.Appointment.Events;
using ToroEhr.Infrastructure.ErrorHandling;
using ToroEhr.Infrastructure.Guards;
using ToroEhr.Infrastructure.Mediatr.Auth;
using ToroEhr.Services;

namespace ToroEhr.Features.Appointment;

public sealed record ConfirmAppointmentCommand(string Id) : AuthRequest<string>;

internal class ConfirmAppointmentAuth : IAuth<ConfirmAppointmentCommand, string>
{
    public ConfirmAppointmentAuth(Authenticator authenticator)
    {
        var user = authenticator.User;
        AuthorizationGuard.AffirmIsEmployee(user);
    }
}

internal class ConfirmAppointmentHandler : IRequestHandler<ConfirmAppointmentCommand, string>
{
    private readonly IDocumentStore _store;

    public ConfirmAppointmentHandler(IDocumentStore store, EmailService emailService)
    {
        _store = store;
    }

    public async Task<string> Handle(ConfirmAppointmentCommand command, CancellationToken cancellationToken)
    {
        using var session = _store.OpenAsyncSession();

        var (appointment, patient, employee, location) = await LoadEntities(session, command.Id, cancellationToken);

        if (appointment.IsConfirmed() == false)
        {
            var encounter = Domain.Encounter.Create(appointment.PatientId, appointment.EmployeeId,
                appointment.LocationId, location.Classification, appointment.StartAt);
            await session.StoreAsync(encounter, cancellationToken);

            appointment.Confirm(encounter.Id);
            await StoreConfirmationNotification(session, appointment, patient, employee, location, cancellationToken);

            // create in-app notification for patient
            await NotificationService.CreateAppointmentConfirmedNotification(session, patient.Id, appointment.Id, DateTime.UtcNow);

            await session.SaveChangesAsync(cancellationToken);
        }

        return appointment.Id;
    }

    private static async Task<(Domain.Appointment appointment, Domain.Patient patient, Domain.Employee employee,
            Domain.Location location)>
        LoadEntities(IAsyncDocumentSession session, string appointmentId, CancellationToken ct)
    {
        var appointment = await session
            .Include<Domain.Appointment>(x => x.PatientId)
            .Include<Domain.Appointment>(x => x.LocationId)
            .Include<Domain.Appointment>(x => x.EmployeeId)
            .LoadAsync<Domain.Appointment>(appointmentId, ct);

        Guard.AgainstNotFound(appointment,
            new AppError("Appointments.NotFound", $"Appointment with 'id:' {appointmentId} not found!"));

        var patient = await session.LoadAsync<Domain.Patient>(appointment.PatientId, ct);
        var employee = await session.LoadAsync<Domain.Employee>(appointment.EmployeeId, ct);
        var location = await session.LoadAsync<Domain.Location>(appointment.LocationId, ct);

        return (appointment, patient, employee, location);
    }

    private static async Task StoreConfirmationNotification(
        IAsyncDocumentSession session,
        Domain.Appointment appointment,
        Domain.Patient patient,
        Domain.Employee employee,
        Domain.Location location,
        CancellationToken ct)
    {
        if (patient.PreferredContactMethod == "Text" && patient.PhoneNumbers.Any(x => x.IsPrimary))
        {
            await session.StoreAsync(
                new SendSmsAppointmentConfirmed(patient, employee, appointment, location.FormattedAddress), ct);
        }
        else
        {
            await session.StoreAsync(
                new SendEmailAppointmentConfirmed(patient, employee, appointment, location.FormattedAddress,
                    appointment.DurationInMinutes), ct);
        }
    }
}