using FluentValidation;
using MediatR;
using Raven.Client.Documents.Session;
using Raven.Client.Documents;
using ToroEhr.Domain;
using ToroEhr.Infrastructure.Guards;
using ToroEhr.Infrastructure.Mediatr.Auth;
using ToroEhr.Services;
using ToroEhr.ValueObjects;
using ToroEhr.Infrastructure;

namespace ToroEhr.Features.Encounter;

public sealed record SendEncounterMessageToPractitionerCommand(string MessageId, string EncounterId, string Subject,
    string Message, List<IFormFile>? Attachments)
    : AuthRequest<Unit>;

internal sealed class SendEncounterMessageToPractitionerAuth : IAuth<SendEncounterMessageToPractitionerCommand, Unit>
{
    public SendEncounterMessageToPractitionerAuth(Authenticator authenticator)
    {
        var user = authenticator.User;
        AuthorizationGuard.AffirmIsPractitioner(user);
    }
}

internal sealed class SendEncounterMessageToPractitionerValidator : AbstractValidator<SendEncounterMessageToPractitionerCommand>
{
    public SendEncounterMessageToPractitionerValidator()
    {
        RuleFor(x => x.MessageId).NotEmpty();
        RuleFor(x => x.EncounterId).NotEmpty();
        RuleFor(x => x.Subject).NotEmpty();
        RuleFor(x => x.Message).NotEmpty();
    }
}

internal sealed class SendEncounterMessageToPractitionerHandler : IRequestHandler<SendEncounterMessageToPractitionerCommand, Unit>
{
    private readonly IDocumentStore _store;
    private readonly S3FileService _fileService;
    private readonly EmailService _emailService;
    private readonly UserRequestSession _user;

    public SendEncounterMessageToPractitionerHandler(IDocumentStore store, Authenticator authenticator, S3FileService fileService, EmailService emailService)
    {
        _store = store;
        _fileService = fileService;
        _emailService = emailService;
        _user = authenticator.User;

    }

    public async Task<Unit> Handle(SendEncounterMessageToPractitionerCommand command, CancellationToken cancellationToken)
    {
        using IAsyncDocumentSession session = _store.OpenAsyncSession();

        var patient = await session.LoadAsync<Domain.Patient>(_user.PatientId, cancellationToken);
        var encounter = await session.LoadAsync<Domain.Encounter>(command.EncounterId, cancellationToken);

        Sender sender = new Sender(true, encounter.PractitionerId, _user.FullName, _user.Email);

        var attachments = new List<string>();
        if (command.Attachments != null)
        {
            foreach (var attachment in command.Attachments)
            {
                var filePath = $"patients/{_user.PatientId}/encounters/{command.EncounterId}/emails/{attachment.FileName}";
                await using var stream = attachment.OpenReadStream();
                await _fileService.UploadFile(stream, attachment.ContentType, Config.S3.AppFilesBucketName, filePath);
                attachments.Add(filePath);
            }
        }
        var message = EncounterEmail.Create(_user.PatientId!, sender, command.EncounterId, command.Subject,
            command.Message, DateTimeOffset.UtcNow, attachments, command.MessageId);

        await session.StoreAsync(message, cancellationToken);
        await session.SaveChangesAsync(cancellationToken);

        return Unit.Value;
    }
}