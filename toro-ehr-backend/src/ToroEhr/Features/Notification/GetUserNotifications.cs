using MediatR;
using Raven.Client.Documents;
using Raven.Client.Documents.Linq;
using Raven.Client.Documents.Session;
using ToroEhr.Enums;
using ToroEhr.Infrastructure.Mediatr.Auth;
using ToroEhr.Shared;

namespace ToroEhr.Features.Notification;

public sealed record GetUserNotificationsQuery(PagedSearchParams PagedSearchParams) : AuthRequest<PaginatedList<NotificationResponse>>;

public sealed record NotificationResponse(
    string Id,
    NotificationType NotificationType,
    NotificationStatus Status,
    string Title,
    string Message,
    string? RelatedEntityId,
    string? RelatedEntityType,
    DateTime CreatedAt,
    DateTime? ReadAt,
    bool ActionCompleted);

internal sealed class GetUserNotificationsAuth : IAuth<GetUserNotificationsQuery, PaginatedList<NotificationResponse>>
{
    public GetUserNotificationsAuth(Authenticator authenticator)
    {
        // any authenticated user can get their own notifications
        var user = authenticator.User;
    }
}

internal sealed class GetUserNotificationsHandler : IRequestHandler<GetUserNotificationsQuery, PaginatedList<NotificationResponse>>
{
    private readonly IDocumentStore _store;
    private readonly UserRequestSession _user;

    public GetUserNotificationsHandler(IDocumentStore store, Authenticator authenticator)
    {
        _store = store;
        _user = authenticator.User;
    }

    public async Task<PaginatedList<NotificationResponse>> Handle(GetUserNotificationsQuery query, CancellationToken cancellationToken)
    {
        using IAsyncDocumentSession session = _store.OpenAsyncSession();

        // determine user ID based on user type
        string userId = _user.IsPatient ? _user.PatientId! : _user.EmployeeId!;

        IRavenQueryable<Domain.Notification> dbQuery = session.Query<Domain.Notification>()
            .Where(x => x.UserId == userId)
            .OrderByDescending(x => x.CreatedAt);

        var notifications = await dbQuery
            .Statistics(out QueryStatistics stats)
            .Skip((query.PagedSearchParams.PageNumber - 1) * query.PagedSearchParams.PageSize)
            .Take(query.PagedSearchParams.PageSize)
            .ToListAsync(cancellationToken);

        var response = notifications.Select(x => new NotificationResponse(
            x.Id,
            NotificationType.FromName(x.NotificationType),
            NotificationStatus.FromName(x.Status),
            x.Title,
            x.Message,
            x.RelatedEntityId,
            x.RelatedEntityType,
            x.CreatedAt,
            x.ReadAt)).ToList();

        return PaginatedList<NotificationResponse>.Create(
            response,
            stats.TotalResults,
            query.PagedSearchParams.PageNumber,
            query.PagedSearchParams.PageSize);
    }
}
