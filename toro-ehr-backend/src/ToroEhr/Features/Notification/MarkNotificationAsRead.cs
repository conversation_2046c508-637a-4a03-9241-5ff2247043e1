using FluentValidation;
using MediatR;
using Raven.Client.Documents;
using Raven.Client.Documents.Session;
using ToroEhr.Infrastructure.Guards;
using ToroEhr.Infrastructure.Mediatr.Auth;

namespace ToroEhr.Features.Notification;

public sealed record MarkNotificationAsReadCommand(string NotificationId) : AuthRequest<Unit>;

internal sealed class MarkNotificationAsReadAuth : IAuth<MarkNotificationAsReadCommand, Unit>
{
    public MarkNotificationAsReadAuth(Authenticator authenticator)
    {
        // any authenticated user can mark their own notifications as read
        var user = authenticator.User;
    }
}

internal sealed class MarkNotificationAsReadValidator : AbstractValidator<MarkNotificationAsReadCommand>
{
    public MarkNotificationAsReadValidator()
    {
        RuleFor(x => x.NotificationId).NotEmpty();
    }
}

internal sealed class MarkNotificationAsReadHandler : IRequestHandler<MarkNotificationAsReadCommand, Unit>
{
    private readonly IDocumentStore _store;
    private readonly UserRequestSession _user;

    public MarkNotificationAsReadHandler(IDocumentStore store, Authenticator authenticator)
    {
        _store = store;
        _user = authenticator.User;
    }

    public async Task<Unit> Handle(MarkNotificationAsReadCommand command, CancellationToken cancellationToken)
    {
        using IAsyncDocumentSession session = _store.OpenAsyncSession();

        Domain.Notification notification = await session.LoadAsync<Domain.Notification>(command.NotificationId, cancellationToken);
        Guard.AgainstNotFound(notification, new("Notification.NotFound", $"Notification with id '{command.NotificationId}' not found"));

        // ensure user can only mark their own notifications as read
        string userId = _user.IsPatient ? _user.PatientId! : _user.EmployeeId!;
        if (notification.UserId != userId)
        {
            throw new UnauthorizedAccessException("You can only mark your own notifications as read");
        }

        notification.MarkAsRead(command.Timestamp);

        await session.SaveChangesAsync(cancellationToken);

        return Unit.Value;
    }
}
